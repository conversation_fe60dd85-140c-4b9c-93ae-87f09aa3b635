import os
import json
import urllib.request
import urllib.parse
from typing import Any
from client.comfyui import ComfyUI
from mcp.server.fastmcp import FastMCP
from dotenv import load_dotenv

load_dotenv()

mcp = FastMCP("comfyui")

@mcp.tool()
async def text_to_image(prompt: str, seed: int, steps: int, cfg: float, denoise: float, return_prompt_id: bool = False) -> Any:
    """Generate an image from a prompt.

    Args:
        prompt: The prompt to generate the image from.
        seed: The seed to use for the image generation.
        steps: The number of steps to use for the image generation.
        cfg: The CFG scale to use for the image generation.
        denoise: The denoise strength to use for the image generation.
        return_prompt_id: Whether to return the prompt ID along with images for status tracking.
    """
    auth = os.environ.get("COMFYUI_AUTHENTICATION")
    comfy = ComfyUI(
        url=f'http://{os.environ.get("COMFYUI_HOST", "localhost")}:{os.environ.get("COMFYUI_PORT", 8188)}',
        authentication=auth
    )
    result = await comfy.process_workflow(
        "text_to_image",
        {"prompt": prompt, "seed": seed, "steps": steps, "cfg": cfg, "denoise": denoise},
        return_url=True,  # Always return URLs for better integration
        return_prompt_id=return_prompt_id
    )
    return result

@mcp.tool()
async def download_image(url: str, save_path: str) -> Any:
    """Download an image from a URL and save it to a file.
    
    Args:
        url: The URL of the image to download.
        save_path: The absolute path to save the image to. Must be an absolute path, otherwise the image will be saved relative to the server location.
    """
    urllib.request.urlretrieve(url, save_path)
    return {"success": True}

@mcp.tool()
async def run_workflow_from_file(file_path: str, return_prompt_id: bool = False) -> Any:
    """Run a workflow from a file.

    Args:
        file_path: The absolute path to the file to run.
        return_prompt_id: Whether to return the prompt ID along with images for status tracking.
    """
    with open(file_path, "r", encoding="utf-8") as f:
        workflow = json.load(f)

    auth = os.environ.get("COMFYUI_AUTHENTICATION")
    comfy = ComfyUI(
        url=f'http://{os.environ.get("COMFYUI_HOST", "localhost")}:{os.environ.get("COMFYUI_PORT", 8188)}',
        authentication=auth
    )
    result = await comfy.process_workflow(
        workflow,
        {},
        return_url=True,  # Always return URLs for better integration
        return_prompt_id=return_prompt_id
    )
    return result

@mcp.tool()
async def run_workflow_from_json(json_data: dict, return_prompt_id: bool = False) -> Any:
    """Run a workflow from a JSON data.

    Args:
        json_data: The JSON data to run.
        return_prompt_id: Whether to return the prompt ID along with images for status tracking.
    """
    workflow = json_data

    auth = os.environ.get("COMFYUI_AUTHENTICATION")
    comfy = ComfyUI(
        url=f'http://{os.environ.get("COMFYUI_HOST", "localhost")}:{os.environ.get("COMFYUI_PORT", 8188)}',
        authentication=auth
    )
    result = await comfy.process_workflow(
        workflow,
        {},
        return_url=True,  # Always return URLs for better integration
        return_prompt_id=return_prompt_id
    )
    return result

@mcp.tool()
async def get_task_status(prompt_id: str) -> Any:
    """Get the status of a task by its prompt ID, including output URLs for completed tasks.

    Args:
        prompt_id: The prompt ID of the task to check status for.
    """
    auth = os.environ.get("COMFYUI_AUTHENTICATION")
    comfy = ComfyUI(
        url=f'http://{os.environ.get("COMFYUI_HOST", "localhost")}:{os.environ.get("COMFYUI_PORT", 8188)}',
        authentication=auth
    )

    try:
        # Get task history to check if it's completed
        history = comfy.get_history(prompt_id)

        if prompt_id in history:
            task_data = history[prompt_id]
            outputs = task_data.get("outputs", {})
            
            # Process outputs to generate URLs for files
            output_files = {}
            # Process all output nodes
            for node_id, node_output in outputs.items():
                output_files[node_id] = []

                # Handle images
                if "images" in node_output:
                    for image_info in node_output["images"]:
                        data = {"filename": image_info["filename"], "subfolder": image_info["subfolder"], "type": image_info["type"]}
                        url_values = urllib.parse.urlencode(data)
                        url = f'{comfy.url}/view?{url_values}'
                        
                        output_files[node_id].append({
                            "type": "image",
                            "filename": image_info["filename"],
                            "subfolder": image_info["subfolder"],
                            "url": url
                        })

                # Handle audio files
                if "audio" in node_output:
                    for audio_info in node_output["audio"]:
                        data = {"filename": audio_info["filename"], "subfolder": audio_info["subfolder"], "type": audio_info["type"]}
                        url_values = urllib.parse.urlencode(data)
                        url = f'{comfy.url}/view?{url_values}'
                        
                        output_files[node_id].append({
                            "type": "audio",
                            "filename": audio_info["filename"],
                            "subfolder": audio_info["subfolder"],
                            "url": url
                        })
            
            status = {
                "prompt_id": prompt_id,
                "status": "completed",
                "outputs": output_files,
                "raw_outputs": task_data.get("outputs", {}),  # Keep raw outputs for backward compatibility
                "status_str": task_data.get("status", {}).get("status_str", ""),
                "completed": task_data.get("status", {}).get("completed", False),
                "total_nodes": len(output_files)
            }
            return status
        else:
            # Check if task is in queue
            queue_info = comfy.get_queue()

            # Check running tasks
            for task in queue_info.get("queue_running", []):
                if task[1] == prompt_id:
                    return {
                        "prompt_id": prompt_id,
                        "status": "running",
                        "position": 0
                    }

            # Check pending tasks
            for i, task in enumerate(queue_info.get("queue_pending", [])):
                if task[1] == prompt_id:
                    return {
                        "prompt_id": prompt_id,
                        "status": "pending",
                        "position": i + 1
                    }

            return {
                "prompt_id": prompt_id,
                "status": "not_found",
                "message": "Task not found in queue or history"
            }
    except Exception as e:
        return {
            "prompt_id": prompt_id,
            "status": "error",
            "error": str(e)
        }

@mcp.tool()
async def download_task_files(prompt_id: str, save_directory: str) -> Any:
    """Download all generated files from a completed task.

    Args:
        prompt_id: The prompt ID of the completed task.
        save_directory: The directory to save the downloaded files to.
    """
    auth = os.environ.get("COMFYUI_AUTHENTICATION")
    comfy = ComfyUI(
        url=f'http://{os.environ.get("COMFYUI_HOST", "localhost")}:{os.environ.get("COMFYUI_PORT", 8188)}',
        authentication=auth
    )

    try:
        # Get task history
        history = comfy.get_history(prompt_id)

        if prompt_id not in history:
            return {
                "success": False,
                "error": "Task not found or not completed"
            }

        task_data = history[prompt_id]
        outputs = task_data.get("outputs", {})

        # Create save directory if it doesn't exist
        os.makedirs(save_directory, exist_ok=True)

        downloaded_files = []

        # Download all files from all output nodes
        for node_id, node_output in outputs.items():
            # Handle images
            if "images" in node_output:
                for i, file_info in enumerate(node_output["images"]):
                    # Get file data
                    file_data = comfy.get_image(
                        file_info["filename"],
                        file_info["subfolder"],
                        file_info["type"]
                    )

                    # Generate filename
                    original_filename = file_info["filename"]
                    filename = f"{prompt_id}_{node_id}_{i}_{original_filename}"
                    file_path = os.path.join(save_directory, filename)

                    # Save file
                    with open(file_path, "wb") as f:
                        f.write(file_data)

                    downloaded_files.append({
                        "node_id": node_id,
                        "file_type": "image",
                        "original_filename": original_filename,
                        "saved_path": file_path,
                        "file_size": len(file_data)
                    })

            # Handle audio files
            if "audio" in node_output:
                for i, file_info in enumerate(node_output["audio"]):
                    # Get file data using the same method as images
                    file_data = comfy.get_image(  # ComfyUI uses the same endpoint for all file types
                        file_info["filename"],
                        file_info["subfolder"],
                        file_info["type"]
                    )

                    # Generate filename
                    original_filename = file_info["filename"]
                    filename = f"{prompt_id}_{node_id}_{i}_{original_filename}"
                    file_path = os.path.join(save_directory, filename)

                    # Save file
                    with open(file_path, "wb") as f:
                        f.write(file_data)

                    downloaded_files.append({
                        "node_id": node_id,
                        "file_type": "audio",
                        "original_filename": original_filename,
                        "saved_path": file_path,
                        "file_size": len(file_data)
                    })

        return {
            "success": True,
            "prompt_id": prompt_id,
            "downloaded_files": downloaded_files,
            "total_files": len(downloaded_files)
        }

    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@mcp.tool()
async def upload_file(file_path: str) -> Any:
    """Upload a file to ComfyUI for use in workflows.
    
    Args:
        file_path: The absolute path to the file to upload.
    """
    auth = os.environ.get("COMFYUI_AUTHENTICATION")
    comfy = ComfyUI(
        url=f'http://{os.environ.get("COMFYUI_HOST", "localhost")}:{os.environ.get("COMFYUI_PORT", 8188)}',
        authentication=auth
    )
    
    try:
        result = comfy.upload_file(file_path)
        return result
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    mcp.run(transport=os.environ.get("MCP_TRANSPORT", "stdio"))
