# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# Language
Think in English, but reply in Chinese.

## Overview
This repository implements an MCP (Model Context Protocol) server that integrates with ComfyUI, a powerful node-based interface for Stable Diffusion and other AI models. The server provides tools for generating images, running workflows, and managing task status.

## Architecture
- `src/server.py`: Main MCP server implementation with tools for ComfyUI integration
- `src/client/comfyui.py`: Client library for communicating with ComfyUI API
- `workflows/`: Directory containing JSON workflow definitions
- `src/.env`: Configuration file for ComfyUI connection settings

## Key Components

### Server Tools
1. `text_to_image`: Generate images from text prompts
2. `download_image`: Download images from URLs
3. `run_workflow_from_file`: Execute workflows from JSON files
4. `run_workflow_from_json`: Execute workflows from JSON data
5. `get_task_status`: Check status of running tasks
6. `download_task_files`: Download all files from completed tasks
7. `get_task_outputs_with_urls`: Get output files with download URLs

### Client Methods
1. `process_workflow`: Main method for executing workflows
2. `get_images`: Retrieve generated images from completed workflows
3. `queue_prompt`: Submit prompts to ComfyUI
4. `get_history`: Retrieve task history
5. `get_queue`: Get current queue status

## Development Commands

### Running the Server
```bash
mcp dev src/server.py
```

### Testing
```bash
# Test ComfyUI connection
python src/test_comfyui.py

# Test new features
python test_new_features.py
```

### Building with Docker
```bash
# Build image
docker image build -t mcp/comfyui .

# Run container
docker run -i --rm -p 3001:3000 mcp/comfyui
```

## Configuration
- Edit `src/.env` to set ComfyUI host and port
- Workflow files should be placed in the `workflows` directory
- Set `RETURN_URL=true` to return image URLs instead of binary data
- For Docker deployments, consider using `host.docker.internal` as the ComfyUI host

## Workflow Integration
To add new tools:
1. Create workflow JSON files in the `workflows` directory
2. Declare them as new tools in `src/server.py`
3. When using Docker, rebuild and redeploy images to make new workflows available