import os
import websocket
import json
import uuid
import urllib.parse
import urllib.request
from typing import Dict, Any
from urllib.parse import quote

class ComfyUI:
    def __init__(self, url: str, authentication: str = None):
        self.url = url
        self.authentication = authentication
        self.client_id = str(uuid.uuid4())
        self.headers = {
            "Content-Type": "application/json"
        }
        if authentication:
            self.headers["Authorization"] = authentication

    def get_image(self, filename, subfolder, folder_type):
        data = {"filename": filename, "subfolder": subfolder, "type": folder_type}
        url_values = urllib.parse.urlencode(data)
        url = f"{self.url}/view?{url_values}"
        req = urllib.request.Request(url, headers=self.headers)
        with urllib.request.urlopen(req) as response:
            return response.read()

    def get_history(self, prompt_id):
        url = f"{self.url}/history/{prompt_id}"
        req = urllib.request.Request(url, headers=self.headers)
        with urllib.request.urlopen(req) as response:
            return json.loads(response.read())

    def get_queue(self):
        url = f"{self.url}/queue"
        req = urllib.request.Request(url, headers=self.headers)
        with urllib.request.urlopen(req) as response:
            return json.loads(response.read())
    
    def queue_prompt(self, prompt):
        p = {"prompt": prompt, "client_id": self.client_id}
        data = json.dumps(p).encode("utf-8")
        req = urllib.request.Request(
            f"{self.url}/prompt",
            headers=self.headers,
            data=data
        )
        return json.loads(urllib.request.urlopen(req).read())
    
    async def process_workflow(self, workflow: Any, params: Dict[str, Any], return_url: bool = False, return_prompt_id: bool = False):
        if isinstance(workflow, str):
            workflow_path = os.path.join(os.environ.get("WORKFLOW_DIR", "workflows"), f"{workflow}.json")
            if not os.path.exists(workflow_path):
                raise Exception(f"Workflow {workflow} not found")
            with open(workflow_path, "r", encoding='utf-8') as f:
                prompt = json.load(f)
        else:
            prompt = workflow

        self.update_workflow_params(prompt, params)

        ws = websocket.WebSocket()
        ws_url = f"ws://{os.environ.get("COMFYUI_HOST", "localhost")}:{os.environ.get("COMFYUI_PORT", 8188)}/ws?clientId={self.client_id}"

        if self.authentication:
            ws.connect(ws_url, header=[f"Authorization: {self.authentication}"])
        else:
            ws.connect(ws_url)

        try:
            result = self.get_images(ws, prompt, return_url, return_prompt_id)
            return result
        finally:
            ws.close()

    def get_images(self, ws, prompt, return_url, return_prompt_id=False):
        prompt_id = self.queue_prompt(prompt)["prompt_id"]
        output_files = {}

        while True:
            out = ws.recv()
            if isinstance(out, str):
                message = json.loads(out)
                if message["type"] == "executing":
                    data = message["data"]
                    if data["node"] is None and data["prompt_id"] == prompt_id:
                        break
            else:
                continue

        history = self.get_history(prompt_id)[prompt_id]
        for node_id in history["outputs"]:
            node_output = history["outputs"][node_id]

            # Handle images
            if "images" in node_output:
                if return_url:
                    output_files[node_id] = []
                    for image in node_output["images"]:
                        data = {"filename": image["filename"], "subfolder": image["subfolder"], "type": image["type"]}
                        url_values = urllib.parse.urlencode(data)
                        url = f"{self.url}/view?{url_values}"
                        output_files[node_id].append({
                            "type": "image",
                            "url": url,
                            "filename": image["filename"],
                            "subfolder": image["subfolder"]
                        })
                else:
                    output_files[node_id] = [
                        self.get_image(image["filename"], image["subfolder"], image["type"])
                        for image in node_output["images"]
                    ]

            # Handle audio files
            if "audio" in node_output:
                if return_url:
                    if node_id not in output_files:
                        output_files[node_id] = []
                    for audio in node_output["audio"]:
                        data = {"filename": audio["filename"], "subfolder": audio["subfolder"], "type": audio["type"]}
                        url_values = urllib.parse.urlencode(data)
                        url = f"{self.url}/view?{url_values}"
                        output_files[node_id].append({
                            "type": "audio",
                            "url": url,
                            "filename": audio["filename"],
                            "subfolder": audio["subfolder"]
                        })
                else:
                    if node_id not in output_files:
                        output_files[node_id] = []
                    for audio in node_output["audio"]:
                        # For audio files, we return the URL since we can't easily load binary audio data
                        data = {"filename": audio["filename"], "subfolder": audio["subfolder"], "type": audio["type"]}
                        url_values = urllib.parse.urlencode(data)
                        url = f"{self.url}/view?{url_values}"
                        output_files[node_id].append({
                            "type": "audio",
                            "url": url,
                            "filename": audio["filename"],
                            "subfolder": audio["subfolder"]
                        })

        if return_prompt_id:
            return {
                "prompt_id": prompt_id,
                "outputs": output_files
            }
        else:
            return output_files

    def update_workflow_params(self, prompt, params):
        if not params:
            return

        for node in prompt.values():
            if node["class_type"] == "CLIPTextEncode" and "text" in params:
                if isinstance(node["inputs"]["text"], str):
                    node["inputs"]["text"] = params["text"]
            elif node["class_type"] == "KSampler":
                if "seed" in params:
                    node["inputs"]["seed"] = params["seed"]
                if "steps" in params:
                    node["inputs"]["steps"] = params["steps"]
                if "cfg" in params:
                    node["inputs"]["cfg"] = params["cfg"]
                if "denoise" in params:
                    node["inputs"]["denoise"] = params["denoise"]
            
            elif node["class_type"] == "LoadImage" and "image" in params:
                node["inputs"]["image"] = params["image"]

    def upload_file(self, file_path: str) -> Dict[str, Any]:
        """Upload a file to ComfyUI.
        
        Args:
            file_path: Path to the file to upload
            
        Returns:
            Dictionary containing upload result information
        """
        import mimetypes
        import io
        
        # Determine content type
        content_type, _ = mimetypes.guess_type(file_path)
        if content_type is None:
            content_type = 'application/octet-stream'
            
        # Read file data
        with open(file_path, 'rb') as f:
            file_data = f.read()
            
        # Get filename
        filename = os.path.basename(file_path)
        
        # Create multipart/form-data request
        boundary = '----WebKitFormBoundary7MA4YWxkTrZu0gW'
        headers = {
            'Content-Type': f'multipart/form-data; boundary={boundary}'
        }
        
        if self.authentication:
            headers["Authorization"] = self.authentication
            
        # Create form data
        form_data = (
            f'--{boundary}\r\n'
            f'Content-Disposition: form-data; name="image"; filename="{filename}"\r\n'
            f'Content-Type: {content_type}\r\n\r\n'
        ).encode('utf-8')
        
        form_data += file_data
        form_data += f'\r\n--{boundary}--\r\n'.encode('utf-8')
        
        # Send request
        url = f"{self.url}/upload/image"
        req = urllib.request.Request(url, data=form_data, headers=headers, method='POST')
        
        try:
            with urllib.request.urlopen(req) as response:
                result = json.loads(response.read())
                return {
                    "success": True,
                    "name": result.get("name", filename),
                    "subfolder": result.get("subfolder", ""),
                    "type": result.get("type", "input")
                }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
