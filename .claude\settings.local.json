{"permissions": {"allow": ["Bash(pnpm run typecheck:*)", "Bash(pnpm run lint:*)", "Bash(pnpm run dev:*)", "<PERSON><PERSON>(taskkill:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(exit)", "Bash(npm install:*)", "Bash(pnpm add:*)", "Bash(pnpm approve-builds:*)", "Bash(pnpm run build:server:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(node dist/server/index.js:*)", "Bash(node:*)", "Bash(pnpm remove:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(curl:*)", "Bash(find:*)", "Read(C:\\Users\\<USER>\\Desktop\\comfyui-mcp-server\\**\\*)", "Write(C:\\Users\\<USER>\\Desktop\\comfyui-mcp-server\\**\\*)", "Edit(C:\\Users\\<USER>\\Desktop\\comfyui-mcp-server\\**\\*)", "WebSearch"], "deny": [], "ask": []}}